"""Health check response schema."""

from datetime import timed<PERSON><PERSON>
from typing import List, Optional

from pydantic import BaseModel, field_serializer


class HealthDetail(BaseModel):
    """Model representing health check details for a service component.

    Attributes:
        component: Name of the component being checked
        status: Current status of the component (e.g., 'healthy', 'unhealthy')
        description: Optional description of the component status
        response_time: Time taken for the health check operation
    """

    component: str
    status: str
    description: Optional[str] = None
    response_time: timedelta

    @field_serializer("response_time")
    def serialize_response_time(self, response_time: timedelta) -> str:
        """Serialize response time from timedelta to human-readable string format.

        Args:
            response_time: The response time as a timedelta object

        Returns:
            String representation in format "HH:MM:SS.sssssss"
        """
        total_seconds = response_time.total_seconds()
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:012.7f}"


class HealthResponse(BaseModel):
    """Represents the health check response.

    Attributes:
        status (str): Health status (healthy/unhealthy).
        statusCode (int): HTTP status code.
        appname (str): Application name.
        instname (str): Instance name.
        version (str): Application version.
        releaseID (str): Release identifier.
        notes (str): Additional notes.
        serviceID (str): Service identifier.
        description (str): Service description.
        message (str): Status message.
    """

    status: str
    status_code: int
    appname: str
    instname: str
    version: str
    release_id: str
    notes: str
    service_id: str
    description: str
    message: str
    details: List[HealthDetail]
