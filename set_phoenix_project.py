#!/usr/bin/env python3
"""<PERSON><PERSON><PERSON> to set a custom Phoenix project name."""

import os
import sys

def set_project_name(project_name):
    """Set the Phoenix project name as an environment variable."""
    os.environ["PHOENIX_PROJECT_NAME"] = project_name
    print(f"✅ Phoenix project name set to: {project_name}")
    
    # Test the configuration
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from src.config.config import config
        print(f"📊 Current project name in config: {config.PHOENIX_PROJECT_NAME}")
        print(f"🔗 Dashboard URL: {config.PHOENIX_COLLECTOR_ENDPOINT}")
        print(f"\n🎯 Your traces will now appear under project: '{project_name}'")
        
        # Quick test
        from src.config.phoenix_config import phoenix_config
        phoenix_config.configure_phoenix_tracing()
        
        tracer = phoenix_config.get_tracer("project-test")
        with tracer.start_as_current_span("project-setup-test") as span:
            span.set_attribute("project.name", project_name)
            span.set_attribute("setup.test", True)
        
        print("✅ Test trace sent successfully!")
        phoenix_config.shutdown()
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")

def main():
    """Main function to set Phoenix project name."""
    if len(sys.argv) != 2:
        print("Usage: python set_phoenix_project.py <project_name>")
        print("\nExample:")
        print("  python set_phoenix_project.py my-custom-project")
        print("  python set_phoenix_project.py terrabloom-production")
        print("  python set_phoenix_project.py terrabloom-dev-john")
        sys.exit(1)
    
    project_name = sys.argv[1]
    
    print("Phoenix Project Name Configuration")
    print("=" * 40)
    print(f"Setting project name to: {project_name}")
    print()
    
    set_project_name(project_name)
    
    print("\n" + "=" * 40)
    print("To make this permanent, set the environment variable:")
    print(f"export PHOENIX_PROJECT_NAME={project_name}")
    print("\nOr add it to your .env file:")
    print(f"PHOENIX_PROJECT_NAME={project_name}")

if __name__ == "__main__":
    main()
