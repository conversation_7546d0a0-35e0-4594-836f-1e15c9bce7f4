"""API application entry point module for the RAG system.

This module sets up the Litestar app, initializes core services via the ServiceContainer,
and provides dependency injection for controllers.
"""

from litestar import Litestar
from litestar.contrib.opentelemetry import (
    OpenTelemetryConfig,
    OpenTelemetryPlugin,
)
from litestar.di import Provide
from litestar.middleware import DefineMiddleware
from litestar.plugins.prometheus import <PERSON>metheusConfig, PrometheusController
from loguru import logger

from infrastructure.bootstrap import bootstrapper
from infrastructure.config import ConfigService
from infrastructure.controllers.admin_controller import AdminController
from infrastructure.controllers.health_controller import HealthController
from infrastructure.handler import global_exception_handler
from infrastructure.middleware.context_middleware import (
    ApplicationContextMiddleware,
)
from infrastructure.middleware.log_enricher import LogEnricherMiddleware
from infrastructure.middleware.logrequest import LogRequestBodyMiddleware
from src.api.controllers.ask import AskController
from src.api.controllers.config import ConfigController
from src.api.controllers.conversation import Conversation<PERSON><PERSON>roller
from src.api.controllers.schema import <PERSON>hemaController
from src.api.service_container import service_container
from src.config.config import Config
from src.config.phoenix_config import phoenix_config
from src.database.connection import DatabaseManager
from src.llm.chat_models import LLMManager
from src.vector_store.retriever import IVFFAISSRetriever


def initialize_app_services() -> None:
    """Initialize services for the application."""
    # Initialize bootstrapper if not already initialized
    bootstrapper.initialize()

    # Initialize Phoenix tracing
    logger.info("Initializing Phoenix tracing...")
    try:
        phoenix_config.configure_phoenix_tracing()
        logger.info("Phoenix tracing initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Phoenix tracing: {e}")

    # Initialize service container with the config from bootstrapper
    if not service_container.is_initialized():
        service_container.initialize(config_service=bootstrapper.get_config())


def get_llm_manager() -> LLMManager:
    """Dependency provider for LLMManager from the ServiceContainer."""
    return service_container.get_llm_manager()


def get_db_manager() -> DatabaseManager:
    """Dependency provider for DatabaseManager from the ServiceContainer."""
    return service_container.get_db_manager()


def get_retriever() -> IVFFAISSRetriever:
    """Dependency provider for IVFFAISSRetriever from the ServiceContainer."""
    return service_container.get_retriever()


def get_config() -> ConfigService:
    """Dependency provider for ConfigService from the ServiceContainer."""
    return service_container.get_config()


def get_rag_config() -> Config:
    """Dependency provider for RAG Config from the ServiceContainer."""
    return service_container.get_rag_config()


def shutdown_app_services() -> None:
    """Shutdown services for the application."""
    logger.info("Shutting down application services...")
    try:
        phoenix_config.shutdown()
        logger.info("Phoenix tracing shutdown successfully")
    except Exception as e:
        logger.error(f"Failed to shutdown Phoenix tracing: {e}")


class CustomPrometheusController(PrometheusController):
    """Custom Prometheus controller to expose metrics at a specific path."""

    path = "/admin/metrics"
    include_in_schema = False


# Set up OpenTelemetry
open_telemetry_config = OpenTelemetryConfig()
open_telemetry_plugin = OpenTelemetryPlugin(open_telemetry_config)

# Set up Prometheus
prometheus_config = PrometheusConfig()


def create_app() -> Litestar:
    """Create the Litestar app with a dynamic API version prefix from config."""
    logger.info("Cartonization API service starting up")
    bootstrapper.initialize()
    config = bootstrapper.get_config()
    version = (
        config.version
        if (hasattr(config, "version") and config.version != "")
        else "v1"
    )
    api_prefix = f"/api/{version}"
    print(f"API version prefix: {api_prefix}")

    return Litestar(
        route_handlers=[
            SchemaController,
            ConversationController,
            ConfigController,
            AskController,
            HealthController,
            AdminController,
            CustomPrometheusController,
        ],
        dependencies={
            "llm_manager": Provide(get_llm_manager),
            "db_manager": Provide(get_db_manager),
            "retriever": Provide(get_retriever),
            "config": Provide(get_config),
            "rag_config": Provide(get_rag_config),
        },
        exception_handlers={Exception: global_exception_handler},
        on_startup=[initialize_app_services],
        on_shutdown=[shutdown_app_services],
        debug=True,
        path=api_prefix,
        plugins=[open_telemetry_plugin],
        middleware=[
            prometheus_config.middleware,
            DefineMiddleware(ApplicationContextMiddleware),
            DefineMiddleware(LogEnricherMiddleware),
            DefineMiddleware(LogRequestBodyMiddleware),
        ],
    )


# Create the app instance using the factory
app = create_app()
