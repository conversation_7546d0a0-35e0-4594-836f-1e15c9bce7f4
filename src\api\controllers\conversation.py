"""Controller for handling conversation-related API endpoints."""

from litestar import Controller, Request, get, post

from src.api.mappers import (
    conversation_list_to_schema,
    conversation_to_schema,
    create_conversation_from_data,
    dict_list_to_conversations,
    dict_to_conversation,
)
from src.api.service_container import service_container


class Conversation<PERSON><PERSON><PERSON>er(Controller):
    """Controller for the /conversations endpoint."""

    path = "/conversations"

    @get("/")
    def list_conversations(self) -> dict:
        """List all conversations."""
        rag_chain = service_container.get_rag_chain()
        conversations = dict_list_to_conversations(
            rag_chain.get_all_conversations()
        )
        return conversation_list_to_schema(conversations).model_dump()

    @post("/")
    async def create_conversation(self, request: Request) -> dict:
        """Create a new conversation from the request data."""
        data = await request.json()
        if not data:
            return {
                "error": (
                    "Request body must be a valid JSON object with required fields."
                ),
            }
        return create_conversation_from_data(data)

    @get("/{conv_id:str}")
    def load_conversation(self, conv_id: str) -> dict:
        """Load a conversation by its ID and return its data or an error message."""
        rag_chain = service_container.get_rag_chain()
        success = rag_chain.load_conversation(conv_id)
        if not success:
            return {"error": "Conversation not found"}
        conv = rag_chain.get_conversation(conv_id)
        if conv:
            return conversation_to_schema(
                dict_to_conversation(conv)
            ).model_dump()
        return {"success": True}

    @post("/{conv_id:str}/delete")
    def delete_conversation(self, conv_id: str) -> dict:
        """Delete a conversation by its ID and return a success or error message."""
        rag_chain = service_container.get_rag_chain()
        success = rag_chain.delete_conversation(conv_id)
        if not success:
            return {"error": "Conversation not found"}
        return {"success": True}
