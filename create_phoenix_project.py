#!/usr/bin/env python3
"""<PERSON><PERSON><PERSON> to create a new Phoenix project and configure tracing."""

import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def create_phoenix_project(project_name):
    """Create a new Phoenix project using the Phoenix client."""
    print(f"🚀 Creating Phoenix project: {project_name}")
    
    try:
        # Import Phoenix client
        from phoenix.client import Client
        from src.config.config import config
        
        # Create Phoenix client
        client = Client(
            api_key=config.PHOENIX_API_KEY,
            base_url=config.PHOENIX_COLLECTOR_ENDPOINT
        )
        
        print(f"✅ Connected to Phoenix at: {config.PHOENIX_COLLECTOR_ENDPOINT}")
        
        # Try to create the project
        try:
            # List existing projects first
            projects = client.get_projects()
            existing_project_names = [p.name for p in projects]
            
            print(f"📋 Existing projects: {existing_project_names}")
            
            if project_name in existing_project_names:
                print(f"✅ Project '{project_name}' already exists!")
            else:
                # Create new project
                new_project = client.create_project(name=project_name)
                print(f"✅ Created new project: {new_project.name}")
                
        except Exception as e:
            print(f"⚠️  Could not create project via API: {e}")
            print("💡 You may need to create the project manually in the Phoenix dashboard")
            
        # Set environment variables for the new project
        os.environ["PHOENIX_PROJECT_NAME"] = project_name
        os.environ["PHOENIX_PROJECT"] = project_name
        
        # Test sending a trace to the new project
        print(f"\n🧪 Testing trace to project: {project_name}")
        
        from src.config.phoenix_config import phoenix_config
        phoenix_config.configure_phoenix_tracing()
        
        tracer = phoenix_config.get_tracer("project-creation-test")
        with tracer.start_as_current_span("new-project-test") as span:
            span.set_attribute("project.name", project_name)
            span.set_attribute("test.type", "project_creation")
            span.set_attribute("created.by", "create_phoenix_project.py")
            
        print("✅ Test trace sent successfully!")
        phoenix_config.shutdown()
        
        print(f"\n🎯 Project '{project_name}' is ready!")
        print(f"🔗 View at: {config.PHOENIX_COLLECTOR_ENDPOINT}")
        
        return True
        
    except ImportError:
        print("❌ Phoenix client not available. Using alternative method...")
        return create_project_alternative(project_name)
    except Exception as e:
        print(f"❌ Error creating project: {e}")
        return create_project_alternative(project_name)

def create_project_alternative(project_name):
    """Alternative method to configure project without Phoenix client."""
    print(f"\n🔄 Using alternative project configuration for: {project_name}")
    
    try:
        from src.config.config import config
        
        # Set all possible environment variables
        os.environ["PHOENIX_PROJECT_NAME"] = project_name
        os.environ["PHOENIX_PROJECT"] = project_name
        os.environ["ARIZE_PHOENIX_PROJECT_NAME"] = project_name
        
        # Send a trace with project metadata
        from src.config.phoenix_config import phoenix_config
        phoenix_config.configure_phoenix_tracing()
        
        tracer = phoenix_config.get_tracer("project-setup")
        with tracer.start_as_current_span("project-initialization") as span:
            span.set_attribute("phoenix.project", project_name)
            span.set_attribute("project.name", project_name)
            span.set_attribute("service.name", "terrabloom-rag")
            span.set_attribute("setup.method", "alternative")
            
        print("✅ Project configuration trace sent!")
        phoenix_config.shutdown()
        
        print(f"\n📝 Manual Steps Required:")
        print(f"1. Go to: {config.PHOENIX_COLLECTOR_ENDPOINT}")
        print(f"2. Look for traces with project name: {project_name}")
        print(f"3. If the project doesn't appear, create it manually in the dashboard")
        print(f"4. Set environment variable: export PHOENIX_PROJECT_NAME={project_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Alternative method failed: {e}")
        return False

def main():
    """Main function to create Phoenix project."""
    if len(sys.argv) != 2:
        print("Usage: python create_phoenix_project.py <project_name>")
        print("\nExample:")
        print("  python create_phoenix_project.py my-new-project")
        print("  python create_phoenix_project.py terrabloom-production")
        sys.exit(1)
    
    project_name = sys.argv[1]
    
    print("Phoenix Project Creation")
    print("=" * 40)
    
    success = create_phoenix_project(project_name)
    
    if success:
        print(f"\n✅ Project setup completed for: {project_name}")
        print("\n🚀 Next steps:")
        print("1. Start your application")
        print("2. Use the RAG functionality to generate traces")
        print("3. Check the Phoenix dashboard for your new project")
    else:
        print(f"\n❌ Project setup failed for: {project_name}")

if __name__ == "__main__":
    main()
