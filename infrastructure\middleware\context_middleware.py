"""Application context middleware for managing request-specific business context.

This middleware extracts relevant headers from the request and creates an
ApplicationContext instance, which is then attached to the request state.
This allows downstream handlers to access the business context easily.
"""

from starlette.middleware.base import BaseHTTPMiddleware

from infrastructure.application_context import ApplicationContext


class ApplicationContextMiddleware(BaseHTTPMiddleware):
    """Middleware to manage application context for each request."""

    async def dispatch(self, request, call_next):
        """Process headers and attach business context to request state.

        Args:
            request: The HTTP request
            call_next: The next middleware/handler in the chain

        Returns:
            HTTP response
        """
        headers = dict(request.headers)

        application_context = ApplicationContext.get_from_headers(headers)

        request.state.context = application_context
        response = await call_next(request)

        return response
