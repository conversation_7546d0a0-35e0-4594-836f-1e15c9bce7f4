"""Configuration management for the RAG system."""

import os
from typing import Any, Dict


class Config:
    """Configuration class for the RAG application."""

    def __init__(self):
        """Initialize Config with environment variables and default settings."""
        # API Keys
        self.GEMINI_API_KEY = os.getenv(
            "GEMINI_API_KEY", "AIzaSyBc_8Ls8yQQsgOgeMusRW3Y8jcC3EO1E_k"
        )
        self.OPENAI_API_KEY = os.getenv(
            "OPENAI_API_KEY",
            "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",
        )  # Add OpenAI API Key
        self.GOOGLE_API_KEY = os.getenv(
            "GOOGLE_API_KEY", "AIzaSyCKHLCrRFIlREEr37RMuqf83E0ezWxdghY"
        )
        self.GROQ_API_KEY = os.getenv(
            "GROQ_API_KEY",
            "********************************************************",
        )
        self.LANGCHAIN_API_KEY = os.getenv(
            "LANGCHAIN_API_KEY",
            "***************************************************",
        )  # Add LangSmith API Key

        # LangSmith Configuration
        self.LANGCHAIN_TRACING_V2 = os.getenv("LANGCHAIN_TRACING_V2", "true")
        self.LANGCHAIN_ENDPOINT = os.getenv(
            "LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com"
        )
        self.LANGCHAIN_PROJECT = os.getenv(
            "LANGCHAIN_PROJECT", "terrabloom-rag"
        )

        # Phoenix Arize Configuration
        self.PHOENIX_API_KEY = os.getenv(
            "PHOENIX_API_KEY",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJBcGlLZXk6MyJ9.rWYllJvKNrACzFs6bi6u55C7W3A9ZyM_ucU9HJ381E0"
        )
        self.PHOENIX_COLLECTOR_ENDPOINT = os.getenv(
            "PHOENIX_COLLECTOR_ENDPOINT",
            "https://app.phoenix.arize.com/s/christojm-us"
        )
        self.PHOENIX_PROJECT_NAME = os.getenv(
            "PHOENIX_PROJECT_NAME", "terrabloom-rag-christojm"
        )
        self.PHOENIX_TRACING_ENABLED = os.getenv(
            "PHOENIX_TRACING_ENABLED", "true"
        ).lower() == "true"
        self.PHOENIX_LOCAL_ENABLED = os.getenv(
            "PHOENIX_LOCAL_ENABLED", "false"
        ).lower() == "true"

        # Database Configuration
        self.DB_USER = os.getenv("DB_USER", "usr_reporting")
        self.DB_PASSWORD = os.getenv("DB_PASSWORD", "Atdd5v3ecsr3p")
        self.DB_HOST = os.getenv("DB_HOST", "alspgbdvit01q.ohl.com")
        self.DB_NAME = os.getenv("DB_NAME", "vite_reporting_r_qa")
        self.DB_PORT = int(os.getenv("DB_PORT", "6432"))

        # Model Configuration
        self.LLM_MODEL = "gemini-2.0-flash"  # Gemini model
        self.OPENROUTER_MODEL = "deepseek/deepseek-chat-v3-0324:free"
        self.EMBEDDING_MODEL = "models/text-embedding-004"
        self.RERANKER_MODEL = "cross-encoder/ms-marco-MiniLM-L6-v2"
        self.TEMPERATURE = 0

        # FAISS Configuration
        self.EMBEDDING_DIM = 768
        self.NLIST = 50
        self.SCHEMA_STORE_PATH = "ivf_schema_store"
        self.TABLE_SCHEMA_PATH = "src/data/table_schema.csv"

        # Retriever Configuration
        self.RETRIEVER_K = 50
        self.RERANKER_K = 5

    def get_db_url(self) -> str:
        """Get database connection URL."""
        return f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    def set_environment_variables(self):
        """Set environment variables for API keys and DB config."""
        os.environ["GEMINI_API_KEY"] = self.GEMINI_API_KEY
        os.environ["GOOGLE_API_KEY"] = self.GOOGLE_API_KEY
        os.environ["GROQ_API_KEY"] = self.GROQ_API_KEY
        os.environ["LANGCHAIN_API_KEY"] = self.LANGCHAIN_API_KEY
        os.environ["LANGCHAIN_TRACING_V2"] = self.LANGCHAIN_TRACING_V2
        os.environ["LANGCHAIN_ENDPOINT"] = self.LANGCHAIN_ENDPOINT
        os.environ["LANGCHAIN_PROJECT"] = self.LANGCHAIN_PROJECT
        os.environ["OPENAI_API_KEY"] = self.OPENAI_API_KEY
        os.environ["DB_USER"] = self.DB_USER
        os.environ["DB_PASSWORD"] = self.DB_PASSWORD
        os.environ["DB_HOST"] = self.DB_HOST
        os.environ["DB_NAME"] = self.DB_NAME
        os.environ["DB_PORT"] = str(self.DB_PORT)

        # Set Phoenix environment variables
        os.environ["PHOENIX_API_KEY"] = self.PHOENIX_API_KEY
        os.environ["PHOENIX_COLLECTOR_ENDPOINT"] = self.PHOENIX_COLLECTOR_ENDPOINT
        os.environ["PHOENIX_PROJECT_NAME"] = self.PHOENIX_PROJECT_NAME
        os.environ["PHOENIX_TRACING_ENABLED"] = str(self.PHOENIX_TRACING_ENABLED)
        os.environ["PHOENIX_LOCAL_ENABLED"] = str(self.PHOENIX_LOCAL_ENABLED)

    def foreign_key_patterns(self) -> Dict[str, Any]:
        """Get foreign key patterns for table schema."""
        return {
            # Core entity references
            "customerid": "customersetup.customers.id",
            "accountid": "customersetup.accounts.id",
            "businessunitid": "customersetup.businessunits.id",
            "warehouseid": "customersetup.warehouses.id",
            "locationid": "customersetup.locations.id",
            "vendorid": "customersetup.vendor.id",
            "buildingid": "customersetup.building.id",
            # Orders related references
            "orderid": "orders.orders.id",
            "fulfillmentid": "orders.fulfillments.id",
            "shippingid": "orders.shipping.id",
            "orderdetailid": "orders.orderdetails.id",
            "cartinformationid": "orders.cartinformation.id",
            "customerinformationid": "orders.customerinformation.id",
            "paymentinformationid": "orders.paymentinformation.id",
            "fulfillmentorderdetailid": "orders.fulfillmentorderdetails.id",
            # Items references
            "itemid": "item.itembusinessunit.id",
            "itembusinessunitid": "item.itembusinessunit.id",
            "itemvendorid": "item.itemvendor.id",
            "productgroupbusinessunitid": "item.productgroupbusinessunit.id",
            "itemkitmaintenanceid": "item.itemkitmaintenance.id",
            "iteminboundid": "item.iteminbound.id",
            "itemoutboundid": "item.itemoutbound.id",
            # Shipping references
            "shipmentid": "shipping.shipments.id",
            "packageid": "shipping.packages.id",
            "manifestid": "shipping.manifests.id",
            "carrieraccountsid": "shipping.carrieraccounts.id",
            "parcelid": "shipping.shipments.id",
            "carrierId": "shipping.carrieraccounts.id",
            "shipperid": "shipping.carrieraccounts.id",
            # Returns references
            "returnid": "returns.return.id",
            "returneditemid": "returnsmanagement.returneditems.id",
            "returnorderfulfillmentid": "returnsmanagement.returnorderfulfillment.id",
            "returnssettingid": "returns.returnssetting.id",
            "returnitemid": "returns.returnitem.id",
            "returntaskid": "returns.returntask.id",
            # Picking references
            "picktaskid": "picking.picktask.id",
            "pickingplateid": "picking.pickingplate.id",
            "subtaskid": "picking.subtask.id",
            "plateid": "picking.pickingplate.id",
            # Tracking references
            "carriertrackingid": "tracking.carriertracking.id",
            "carrieractivitytypeid": "tracking.trackingactivity.id",
            # Inventory references
            "cyclecountid": "inventorycontrol.cyclecounttask.id",
            "cyclecountactivityid": "inventorycontrol.cyclecountactivitydetail.id",
            "physicalinventoryid": "inventorycontrol.physicalinventorytask.id",
            "pirequestid": "inventorycontrol.pirequest.id",
            # Document references
            "templateid": "document.labeltemplates.id",
            "typeid": "document.labeltypes.id",
            "shipserviceid": "document.shipservices.id",
            "templatecodeid": "document.templatecodes.id",
            # Organization references
            "organizationid": "customersetup.organizations.id",
            "shiftid": "customersetup.organizationshifts.id",
            "storeid": "customersetup.organizationstores.id",
            # Wave references (related to picking/order processing)
            "waveid": "picking.picktask.id",
            "batchid": "picking.picktask.id",
            # Automation references
            "ordertaskid": "automation.ordertasks.id",
            "ordersubtaskid": "automation.ordersubtasks.id",
            # Tag references
            "tagid": "orders.tags.id",
            "tagvalue": "orders.tagvalues.id",
        }


# Global config instance
config = Config()
config.set_environment_variables()  # Ensure env vars are set at import time
