# Phoenix Arize Configuration
# Copy this file to .env and update the values as needed

# Phoenix API Key (provided by user)
PHOENIX_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJBcGlLZXk6MyJ9.rWYllJvKNrACzFs6bi6u55C7W3A9ZyM_ucU9HJ381E0

# Phoenix Collector Endpoint (provided by user)
PHOENIX_COLLECTOR_ENDPOINT=https://app.phoenix.arize.com/s/christojm-us

# Phoenix Project Name
PHOENIX_PROJECT_NAME=terrabloom-rag

# Enable Phoenix Tracing
PHOENIX_TRACING_ENABLED=true

# Disable local Phoenix instance (using cloud)
PHOENIX_LOCAL_ENABLED=false

# Optional: Phoenix working directory for local instance
# PHOENIX_WORKING_DIR=./phoenix_data

# Optional: Additional client headers
# PHOENIX_CLIENT_HEADERS=header1:value1,header2:value2
