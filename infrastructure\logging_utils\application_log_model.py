"""This module defines the data models for logging application messages and mini profiler messages.
It includes classes for `MiniProfilerMessage` and `ApplicationLogMessage`, which can be serialized to JSON.
"""

import json
import platform
import uuid
from datetime import datetime, timezone


class MiniProfilerMessage:
    """Represents a mini profiler message for logging application performance metrics.
    It includes details such as route ID, route name, start time, duration, machine name,
    custom timings, and custom links.
    """

    def __init__(self, route_id=None, route_name=None):
        """Initializes a new instance of MiniProfilerMessage."""
        self.RouteId = route_id
        self.RouteName = route_name
        self.Started = datetime.now(timezone.utc).isoformat()
        self.DurationMilliseconds = 0.0
        self.MachineName = platform.node()
        self.CustomTimingsJson = None
        self.CustomLinks = {}

    def end(self):
        """Marks the end of the profiling session by calculating the duration in milliseconds.
        This method should be called when the operation being profiled is complete.
        """
        start_time = datetime.fromisoformat(self.Started)

        # Calculate duration using timezone-aware datetime
        self.DurationMilliseconds = (
            datetime.now(timezone.utc) - start_time
        ).total_seconds() * 1000

    def to_dict(self):
        """Converts the MiniProfilerMessage instance to a dictionary representation.
        This is useful for serialization or logging purposes.
        """
        return {
            "RouteId": self.RouteId,
            "RouteName": self.RouteName,
            "Started": self.Started,
            "DurationMilliseconds": self.DurationMilliseconds,
            "MachineName": self.MachineName,
            "CustomTimingsJson": self.CustomTimingsJson,
            "CustomLinks": self.CustomLinks,
        }


class ApplicationLogMessage:
    """Represents a log message for an application, including method name, message,
    input parameters, output parameters, application metrics, additional properties,
    and a folder key value.
    """

    def __init__(self):
        """Initializes a new instance of ApplicationLogMessage with a unique ID and default values.
        The ID is generated using UUID to ensure uniqueness across log messages.
        """
        self.Id = str(uuid.uuid4())
        self.MethodName = None
        self.Message = None
        self.InputParams = {}
        self.OutPutParams = {}
        self.AppMetrics = MiniProfilerMessage()
        self.AdditionalProperties = {}
        self.FolderKeyValue = None

    def to_dict(self):
        """Converts the ApplicationLogMessage instance to a dictionary representation.
        This is useful for serialization or logging purposes.
        """
        return {
            "Id": self.Id,
            "MethodName": self.MethodName,
            "message": self.Message,
            "InputParams": self.InputParams,
            "OutPutParams": self.OutPutParams,
            "AppMetrics": self.AppMetrics.to_dict(),
            "AdditionalProperties": self.AdditionalProperties,
            "FolderKeyValue": self.FolderKeyValue,
        }

    def to_json(self):
        """Serializes the ApplicationLogMessage instance to a JSON string.
        This method uses the `to_dict` method to convert the instance to a dictionary
        """
        return json.dumps(self.to_dict(), default=str)
