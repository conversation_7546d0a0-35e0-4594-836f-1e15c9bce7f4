"""# src/api/controllers/admin_controller.py.

Controller for handling admin requests.

This module defines endpoints for service management and monitoring.
"""

from datetime import timedelta

from litestar import Controller, get

from infrastructure.config import ConfigService
from infrastructure.schemas.health_response import HealthDetail, HealthResponse
from src.api.service_container import service_container


class Admin<PERSON><PERSON>roller(Controller):
    """Controller for handling admin requests.

    Defines endpoints for service management and monitoring.
    """

    path = "/admin"

    @get("/activate", include_in_schema=False)
    def activate(self) -> dict:
        """Admin endpoint to activate the cartonization service.

        This is a placeholder for any future admin functionalities.

        Returns:
            dict: Status and message about activation.
        """
        try:
            if service_container.is_initialized():
                return {
                    "status": "200 OK",
                    "message": "Services already initialized.",
                    "initialized": True,
                }

            service_container.initialize()
            return {
                "status": "200 OK",
                "message": "Cartonization service activated successfully.",
                "initialized": True,
            }
        except Exception as e:
            return {
                "status": "500 Error",
                "message": f"Failed to activate service: {str(e)}",
                "initialized": False,
            }

    @get("/shutdown", include_in_schema=False)
    def shutdown_service(self) -> dict:
        """Admin endpoint to shut down the cartonization service.

        This is a placeholder for any future shutdown functionalities.

        Returns:
            dict: Status and message about shutdown.
        """
        return {
            "status": "200 OK",
            "message": "Service shutdown is not yet implemented.",
        }

    @get("/liveness", include_in_schema=False)
    def liveness_check(self, config: ConfigService) -> dict:
        """Admin endpoint to check if the service is alive.

        This is a placeholder for any future liveness check functionalities.

        Returns:
            dict: Status and message about liveness.
        """
        app_name = config.app_name
        version = config.version
        return HealthResponse(
            status="Healthy",
            status_code=200,
            appname=app_name,
            instname="cartonization",
            version=version,
            release_id="",
            notes="",
            service_id="",
            description="Cartonization service is live",
            message="Service is running.",
            details=[
                HealthDetail(
                    component="self",
                    status="healthy",
                    description=None,
                    response_time=timedelta(microseconds=1.6),
                )
            ],
        ).dict()
