"""Phoenix Arize tracing configuration for the RAG system."""

import os
from typing import Optional

from loguru import logger
from openinference.instrumentation.langchain import Lang<PERSON>hainInstrumentor
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk import trace as trace_sdk
from opentelemetry.sdk.trace.export import ConsoleSpanExporter, SimpleSpanProcessor

# Import the main config to get Phoenix settings
from src.config.config import config as main_config


class PhoenixConfig:
    """Configuration class for Phoenix Arize tracing."""

    def __init__(self):
        """Initialize Phoenix configuration using main config."""
        # Use configuration from main config
        self.PHOENIX_COLLECTOR_ENDPOINT = main_config.PHOENIX_COLLECTOR_ENDPOINT
        self.PHOENIX_PROJECT_NAME = main_config.PHOENIX_PROJECT_NAME
        self.PHOENIX_API_KEY = main_config.PHOENIX_API_KEY
        self.PHOENIX_TRACING_ENABLED = main_config.PHOENIX_TRACING_ENABLED
        self.PHOENIX_LOCAL_ENABLED = main_config.PHOENIX_LOCAL_ENABLED

        # Phoenix client headers for Arize Cloud
        self.PHOENIX_CLIENT_HEADERS = os.getenv("PHOENIX_CLIENT_HEADERS")

        # Phoenix database settings for local instance
        self.PHOENIX_WORKING_DIR = os.getenv(
            "PHOENIX_WORKING_DIR", "./phoenix_data"
        )

        self._tracer_provider: Optional[trace_sdk.TracerProvider] = None
        self._phoenix_session = None

    def start_phoenix_local(self) -> Optional[object]:
        """Start a local Phoenix instance."""
        if not self.PHOENIX_LOCAL_ENABLED:
            logger.info("Phoenix local instance disabled")
            return None

        try:
            # Import phoenix only when needed to avoid syntax errors
            import phoenix as px

            # Start Phoenix server locally
            self._phoenix_session = px.launch_app(
                host="localhost",
                port=6006,
                working_dir=self.PHOENIX_WORKING_DIR,
            )
            logger.info(
                f"Phoenix local instance started at {self._phoenix_session.url}"
            )
            return self._phoenix_session
        except Exception as e:
            logger.error(f"Failed to start Phoenix local instance: {e}")
            return None

    def configure_phoenix_tracing(self) -> None:
        """Configure Phoenix Arize tracing for LangChain."""
        if not self.PHOENIX_TRACING_ENABLED:
            logger.info("Phoenix tracing disabled")
            return

        try:
            # Set up tracer provider
            self._tracer_provider = trace_sdk.TracerProvider()

            # Configure OTLP exporter for Phoenix
            if self.PHOENIX_COLLECTOR_ENDPOINT:
                headers = {}
                if self.PHOENIX_API_KEY:
                    headers["Authorization"] = f"Bearer {self.PHOENIX_API_KEY}"

                # Additional headers if provided
                if self.PHOENIX_CLIENT_HEADERS:
                    for header_pair in self.PHOENIX_CLIENT_HEADERS.split(","):
                        if ":" in header_pair:
                            key, value = header_pair.split(":", 1)
                            headers[key.strip()] = value.strip()

                # Determine the correct endpoint based on the collector URL
                if "app.phoenix.arize.com" in self.PHOENIX_COLLECTOR_ENDPOINT:
                    # For Arize Cloud, use the v1/traces endpoint
                    endpoint = f"{self.PHOENIX_COLLECTOR_ENDPOINT}/v1/traces"
                else:
                    # For local Phoenix, append the endpoint path
                    endpoint = f"{self.PHOENIX_COLLECTOR_ENDPOINT}/v1/traces"

                otlp_exporter = OTLPSpanExporter(
                    endpoint=endpoint,
                    headers=headers,
                )
                self._tracer_provider.add_span_processor(
                    SimpleSpanProcessor(otlp_exporter)
                )
                logger.info(
                    f"Phoenix OTLP exporter configured with endpoint: {endpoint}"
                )

            # Set the tracer provider
            trace.set_tracer_provider(self._tracer_provider)

            # Instrument LangChain
            LangChainInstrumentor().instrument()

            # Set Phoenix project name as environment variable
            if self.PHOENIX_PROJECT_NAME:
                os.environ["PHOENIX_PROJECT_NAME"] = self.PHOENIX_PROJECT_NAME

            logger.info("Phoenix tracing configured successfully")

        except Exception as e:
            logger.error(f"Failed to configure Phoenix tracing: {e}")

    def get_tracer(self, name: str = "terrabloom-rag"):
        """Get a tracer instance."""
        if self._tracer_provider:
            return self._tracer_provider.get_tracer(name)
        return trace.get_tracer(name)

    def shutdown(self) -> None:
        """Shutdown Phoenix tracing and local instance."""
        try:
            if self._tracer_provider:
                self._tracer_provider.shutdown()
                logger.info("Phoenix tracer provider shutdown")

            if self._phoenix_session:
                # Phoenix session doesn't have a direct shutdown method
                # The process will be terminated when the application exits
                logger.info("Phoenix local session will terminate with application")

        except Exception as e:
            logger.error(f"Error during Phoenix shutdown: {e}")


# Global Phoenix configuration instance
phoenix_config = PhoenixConfig()