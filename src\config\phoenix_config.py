"""Phoenix Arize tracing configuration for the RAG system."""

import os
from typing import <PERSON><PERSON>

from loguru import logger

# Import the main config to get Phoenix settings
from src.config.config import config as main_config


class PhoenixConfig:
    """Configuration class for Phoenix Arize tracing."""

    def __init__(self):
        """Initialize Phoenix configuration using main config."""
        # Use configuration from main config
        self.PHOENIX_COLLECTOR_ENDPOINT = main_config.PHOENIX_COLLECTOR_ENDPOINT
        self.PHOENIX_PROJECT_NAME = main_config.PHOENIX_PROJECT_NAME
        self.PHOENIX_API_KEY = main_config.PHOENIX_API_KEY
        self.PHOENIX_TRACING_ENABLED = main_config.PHOENIX_TRACING_ENABLED
        self.PHOENIX_LOCAL_ENABLED = main_config.PHOENIX_LOCAL_ENABLED

        # Phoenix client headers for Arize Cloud
        self.PHOENIX_CLIENT_HEADERS = os.getenv("PHOENIX_CLIENT_HEADERS")

        # Phoenix database settings for local instance
        self.PHOENIX_WORKING_DIR = os.getenv(
            "PHOENIX_WORKING_DIR", "./phoenix_data"
        )

        self._tracer_provider = None
        self._phoenix_session = None

    def start_phoenix_local(self) -> Optional[object]:
        """Start a local Phoenix instance."""
        if not self.PHOENIX_LOCAL_ENABLED:
            logger.info("Phoenix local instance disabled")
            return None

        try:
            # Import phoenix only when needed to avoid syntax errors
            import phoenix as px

            # Start Phoenix server locally
            self._phoenix_session = px.launch_app(
                host="localhost",
                port=6006,
                working_dir=self.PHOENIX_WORKING_DIR,
            )
            logger.info(
                f"Phoenix local instance started at {self._phoenix_session.url}"
            )
            return self._phoenix_session
        except Exception as e:
            logger.error(f"Failed to start Phoenix local instance: {e}")
            return None

    def configure_phoenix_tracing(self) -> None:
        """Configure Phoenix Arize tracing for LangChain using official Phoenix OTEL."""
        if not self.PHOENIX_TRACING_ENABLED:
            logger.info("Phoenix tracing disabled")
            return

        try:
            # Set Phoenix environment variables
            os.environ["PHOENIX_API_KEY"] = self.PHOENIX_API_KEY
            os.environ["PHOENIX_COLLECTOR_ENDPOINT"] = self.PHOENIX_COLLECTOR_ENDPOINT

            logger.info(f"Configuring Phoenix tracing for project: {self.PHOENIX_PROJECT_NAME}")

            # Use the official Phoenix OTEL registration
            from phoenix.otel import register

            # Configure the Phoenix tracer using the official method
            self._tracer_provider = register(
                project_name=self.PHOENIX_PROJECT_NAME,  # This is the key for project organization
                auto_instrument=True  # Auto-instrument based on installed dependencies
            )

            logger.info(f"✅ Phoenix tracing configured successfully")
            logger.info(f"📊 Project: {self.PHOENIX_PROJECT_NAME}")
            logger.info(f"🔗 Endpoint: {self.PHOENIX_COLLECTOR_ENDPOINT}")
            logger.info(f"🔧 Auto-instrumentation: Enabled (LangChain will be traced automatically)")

        except ImportError as e:
            logger.error(f"Phoenix OTEL not available: {e}")
            logger.info("Install with: pip install arize-phoenix-otel")
        except Exception as e:
            logger.error(f"Failed to configure Phoenix tracing: {e}")

    def get_tracer(self, name: str = "terrabloom-rag"):
        """Get a tracer instance."""
        if self._tracer_provider:
            return self._tracer_provider.get_tracer(name)

        # Fallback to default tracer
        from opentelemetry import trace
        return trace.get_tracer(name)

    def shutdown(self) -> None:
        """Shutdown Phoenix tracing and local instance."""
        try:
            if self._tracer_provider:
                # Phoenix OTEL handles shutdown automatically
                logger.info("Phoenix tracer provider will shutdown automatically")

            if self._phoenix_session:
                # Phoenix session doesn't have a direct shutdown method
                # The process will be terminated when the application exits
                logger.info("Phoenix local session will terminate with application")

        except Exception as e:
            logger.error(f"Error during Phoenix shutdown: {e}")


# Global Phoenix configuration instance
phoenix_config = PhoenixConfig()