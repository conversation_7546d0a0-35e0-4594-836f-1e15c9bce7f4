"""Application bootstrapper.

This module provides class-based bootstrapping for the application.
"""

import os

from infrastructure.arg_parser import parse_args
from infrastructure.config import ConfigService
from infrastructure.logging_utils.logger import logger
from infrastructure.server_config import configure_telemetry, run_server


class ApplicationBootstrapper:
    """Bootstrap and manage application resources."""

    def __init__(self, config_path: str = None):
        """Initialize the bootstrapper.

        Args:
            config_path: Path to the configuration file
        """
        # For testing: check if running in pytest environment
        import sys

        is_pytest = "pytest" in sys.modules or any(
            "pytest" in arg for arg in sys.argv
        )

        # Parse command-line arguments if config_path not provided
        if not config_path:
            if is_pytest:
                # In pytest, use environment variable or default config
                config_path = os.environ.get(
                    "CONFIG_FILE_PATH", "profiles/dev/api/appsettings.json"
                )
                self.args = {"config_file_path": config_path}
            else:
                try:
                    args = parse_args()
                    config_path = args.get("config_file_path")
                    self.args = args
                except SystemExit:
                    # Fallback if argparse fails
                    config_path = os.environ.get(
                        "CONFIG_FILE_PATH", "profiles/dev/api/appsettings.json"
                    )
                    self.args = {"config_file_path": config_path}
        else:
            self.args = {"config_file_path": config_path}

        # Create and store config service
        self.config = ConfigService(config_path)
        self.initialized = False
        self.server_config = None  # Initialize in initialize() method

    def initialize(self) -> None:
        """Initialize application resources."""
        if self.initialized:
            return

        # Configure logger - using your existing logger configuration
        logger.configure(
            log_path=self.config.log_path,
            log_level=self.config.log_level,
            log_rotation=self.config.log_rotation,
            log_max_files=self.config.log_max_files,
            enable_console=True,
            console_level="INFO",
            enable_fluentbit=True,
            fluentbit_level="INFO",
        )

        # Log application startup
        logger.info("Cartonization service initializing")

        # Get server configuration
        self.server_config = self.config.get_server_config()

        # Override port if specified in command line
        if self.args.get("port"):
            self.server_config["port"] = self.args["port"]

        # Configure Phoenix tracing first (before other OpenTelemetry setup)
        try:
            from src.config.phoenix_config import phoenix_config
            phoenix_config.configure_phoenix_tracing()
            logger.info("Phoenix tracing configured in bootstrap")
        except Exception as e:
            logger.warning(f"Phoenix tracing configuration failed in bootstrap: {e}")

        # Configure OpenTelemetry - using your existing telemetry configuration
        # This will now add to the existing Phoenix TracerProvider
        configure_telemetry(
            service_name=self.server_config["service_name"],
            endpoint=self.server_config["otlp_endpoint"],
        )

        self.initialized = True
        logger.info("Application resources initialized")

    def get_config(self) -> ConfigService:
        """Get the configuration service."""
        if not self.initialized:
            self.initialize()
        return self.config

    def run(self) -> None:
        """Run the application server."""
        if not self.initialized:
            self.initialize()

        # Start the server - using your existing run_server function
        run_server(
            app_module="src.api.app:app",  # Update this path as needed
            port=self.server_config["port"],
            ssl_keyfile=self.server_config["key_path"],
            ssl_certfile=self.server_config["cert_path"],
        )


# Create singleton instance
bootstrapper = ApplicationBootstrapper()


def bootstrap_application() -> None:
    """Bootstrap the application and start the server.

    This function maintains compatibility with the existing code.
    """
    bootstrapper.run()
